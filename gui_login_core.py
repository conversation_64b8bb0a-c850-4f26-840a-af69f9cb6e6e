#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧中小学自动登录 - GUI核心模块
重构原有登录逻辑，支持GUI回调和多线程
"""

import time
import random
import logging
import os
import sys
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException
from PIL import Image
import io
import threading
import queue

# 导入ChromeDriver管理器
try:
    from chromedriver_manager import chromedriver_manager
except ImportError:
    chromedriver_manager = None

class GUISmartEduLogin:
    """GUI版本的智慧中小学自动登录类"""
    
    def __init__(self, account_info, callback_manager=None):
        """初始化登录类
        
        Args:
            account_info: 账号信息字典
            callback_manager: 回调管理器，用于与GUI通信
        """
        self.account_info = account_info
        self.callback_manager = callback_manager
        
        # 登录配置
        self.LOGIN_URL = "https://auth.smartedu.cn/uias/login"
        self.TARGET_URL = "https://basic.smartedu.cn/training/2025sqpx"
        
        # 账号信息
        self.USERNAME = account_info["username"]
        self.PASSWORD = account_info["password"]
        self.ACCOUNT_NAME = account_info["name"]
        
        # 浏览器相关
        self.driver = None
        self.wait = None
        self.chrome_process = None
        
        # 配置参数
        self.TIMEOUT = 30
        self.MAX_RETRIES = 2
        self.RETRY_DELAY = 3
        
        # 状态控制
        self.is_running = True
        self.countdown_time = 10
        
    def log(self, message, level="INFO"):
        """发送日志消息"""
        if self.callback_manager:
            self.callback_manager.log_callback(f"[{self.ACCOUNT_NAME}] {message}", level)
    
    def update_status(self, message):
        """更新状态"""
        if self.callback_manager:
            self.callback_manager.status_callback(message)
    
    def find_chrome_executable(self):
        """查找Chrome浏览器可执行文件路径"""
        try:
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
                r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
            ]

            for path in chrome_paths:
                expanded_path = os.path.expandvars(path)
                if os.path.exists(expanded_path):
                    self.log(f"找到Chrome浏览器: {expanded_path}")
                    return expanded_path

            self.log("未找到Chrome浏览器，请确保Chrome已安装", "ERROR")
            return None

        except Exception as e:
            self.log(f"查找Chrome浏览器失败: {str(e)}", "ERROR")
            return None

    def setup_driver(self):
        """直接启动Chrome浏览器，无需ChromeDriver"""
        try:
            self.log("正在启动Chrome浏览器...")

            # 查找Chrome浏览器路径
            chrome_path = self.find_chrome_executable()
            if not chrome_path:
                raise Exception("未找到Chrome浏览器，请确保Chrome已正确安装")

            chrome_options = Options()

            # 设置Chrome可执行文件路径
            chrome_options.binary_location = chrome_path

            # 启用远程调试端口
            debug_port = 9222
            chrome_options.add_argument(f'--remote-debugging-port={debug_port}')

            # 基本反检测设置
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])

            # 设置用户代理
            user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            chrome_options.add_argument(f'--user-agent={user_agent}')

            # 其他基本设置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-default-apps')
            chrome_options.add_argument('--disable-infobars')

            # 使用临时用户数据目录，避免与正在运行的Chrome冲突
            import tempfile
            temp_dir = tempfile.mkdtemp()
            chrome_options.add_argument(f'--user-data-dir={temp_dir}')

            try:
                # 直接启动Chrome，不需要ChromeDriver
                self.driver = webdriver.Chrome(options=chrome_options)
                self.log("Chrome浏览器启动成功")
            except Exception as e:
                self.log(f"Chrome浏览器启动失败: {str(e)}", "ERROR")
                # 如果直接启动失败，尝试使用系统默认方式
                try:
                    chrome_options_fallback = Options()
                    chrome_options_fallback.add_argument('--disable-blink-features=AutomationControlled')
                    chrome_options_fallback.add_experimental_option('useAutomationExtension', False)
                    self.driver = webdriver.Chrome(options=chrome_options_fallback)
                    self.log("使用系统默认方式启动Chrome成功")
                except Exception as e2:
                    raise Exception(f"无法启动Chrome浏览器: {str(e2)}")

            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.wait = WebDriverWait(self.driver, self.TIMEOUT)
            self.log("Chrome浏览器初始化完成")

        except Exception as e:
            self.log(f"浏览器初始化失败: {str(e)}", "ERROR")
            raise
    
    def human_like_delay(self, min_delay=0.5, max_delay=2.0):
        """模拟人类操作的随机延迟"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
        return delay
    
    def navigate_to_login(self):
        """导航到登录页面"""
        try:
            self.log(f"正在访问登录页面...")
            self.update_status("访问登录页面")
            
            self.driver.get(self.LOGIN_URL)
            self.wait.until(EC.presence_of_element_located((By.ID, "username")))
            
            self.log("登录页面加载成功")
            self.human_like_delay(1, 3)
            
        except TimeoutException:
            self.log("登录页面加载超时", "ERROR")
            raise
        except Exception as e:
            self.log(f"访问登录页面失败: {str(e)}", "ERROR")
            raise
    
    def fill_login_form(self):
        """填写登录表单"""
        try:
            self.log("开始填写登录信息...")
            self.update_status("填写登录信息")
            
            # 输入用户名
            username_input = self.wait.until(EC.element_to_be_clickable((By.ID, "username")))
            username_input.clear()
            username_input.send_keys(self.USERNAME)
            
            self.log("用户名输入完成")
            self.human_like_delay(0.3, 0.8)
            
            # 输入密码
            password_input = self.driver.find_element(By.ID, "tmpPassword")
            password_input.clear()
            password_input.send_keys(self.PASSWORD)
            
            self.log("密码输入完成")
            self.human_like_delay(0.3, 0.8)
            
            # 勾选同意协议
            agreement_checkbox = self.driver.find_element(By.ID, "agreementCheckbox")
            if not agreement_checkbox.is_selected():
                agreement_checkbox.click()
                self.log("已勾选同意协议")
            
            self.human_like_delay(0.3, 0.6)
            
        except Exception as e:
            self.log(f"填写登录表单失败: {str(e)}", "ERROR")
            raise
    
    def click_login_button(self):
        """点击登录按钮"""
        try:
            self.log("点击登录按钮...")
            self.update_status("点击登录按钮")
            
            login_btn = self.wait.until(EC.element_to_be_clickable((By.ID, "loginBtn")))
            ActionChains(self.driver).move_to_element(login_btn).pause(0.5).click().perform()
            
            self.log("登录按钮已点击")
            time.sleep(3)  # 等待验证码加载
            
        except Exception as e:
            self.log(f"点击登录按钮失败: {str(e)}", "ERROR")
            raise
    
    def wait_for_captcha_manual(self):
        """等待手动验证码操作"""
        try:
            self.log("等待手动验证码操作...")
            self.update_status("等待验证码操作")
            
            # 通知GUI开始倒计时
            if self.callback_manager:
                self.callback_manager.countdown_callback(self.countdown_time)
            
            # 等待倒计时结束
            countdown_remaining = self.countdown_time
            while countdown_remaining > 0 and self.is_running:
                time.sleep(1)
                countdown_remaining -= 1
            
            if not self.is_running:
                return False
            
            self.log("验证码操作时间结束")
            
            # 检查登录状态
            time.sleep(2)
            return self.check_login_success()
            
        except Exception as e:
            self.log(f"验证码处理失败: {str(e)}", "ERROR")
            return False
    
    def check_login_success(self):
        """检查登录是否成功"""
        try:
            self.log("检查登录状态...")
            self.update_status("检查登录状态")
            
            time.sleep(3)
            current_url = self.driver.current_url
            
            if "login" in current_url:
                # 检查错误提示
                try:
                    error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".error-msg, .alert-danger, .login-error")
                    if error_elements:
                        error_text = error_elements[0].text
                        self.log(f"登录失败，错误信息: {error_text}", "WARNING")
                        return False
                except:
                    pass
                
                self.log("仍在登录页面，登录可能失败", "WARNING")
                return False
            else:
                self.log("登录成功，已跳转到其他页面")
                return True
                
        except Exception as e:
            self.log(f"检查登录状态失败: {str(e)}", "ERROR")
            return False
    
    def navigate_to_target_page(self):
        """导航到目标页面"""
        try:
            self.log("正在访问目标页面...")
            self.update_status("访问目标页面")
            
            self.driver.get(self.TARGET_URL)
            time.sleep(5)
            
            current_url = self.driver.current_url
            self.log(f"目标页面加载完成")
            return True
            
        except Exception as e:
            self.log(f"访问目标页面失败: {str(e)}", "ERROR")
            return False
    
    def take_screenshot(self):
        """截图保存"""
        try:
            self.log("开始截图...")
            self.update_status("正在截图")
            
            # 滚动到页面顶部
            self.driver.execute_script("window.scrollTo(0, 0)")
            time.sleep(2)
            
            # 截取全屏
            screenshot = self.driver.get_screenshot_as_png()
            
            # 使用PIL处理图片
            image = Image.open(io.BytesIO(screenshot))
            width, height = image.size
            
            # 截取上半部分
            crop_height = int(height * 0.8)
            upper_half = image.crop((0, 0, width, crop_height))
            
            # 保存截图
            save_dir = "screenshots"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
            
            screenshot_path = f"{save_dir}/{self.ACCOUNT_NAME}.png"
            upper_half.save(screenshot_path)
            
            self.log(f"截图保存成功: {screenshot_path}")
            return screenshot_path
            
        except Exception as e:
            self.log(f"截图失败: {str(e)}", "ERROR")
            return None
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.log("正在关闭浏览器...")
                self.driver.quit()
                self.log("浏览器已关闭")

            # 关闭Chrome进程
            if hasattr(self, 'chrome_process') and self.chrome_process:
                try:
                    self.chrome_process.terminate()
                    self.log("Chrome进程已终止")
                except:
                    pass

        except Exception as e:
            self.log(f"清理资源失败: {str(e)}", "ERROR")
    
    def stop(self):
        """停止执行"""
        self.is_running = False
    
    def set_countdown_time(self, seconds):
        """设置倒计时时间"""
        self.countdown_time = seconds

    def run(self):
        """执行完整的登录流程"""
        try:
            self.log("开始执行自动登录流程...")

            # 1. 初始化浏览器
            if not self.is_running:
                return False
            self.setup_driver()

            # 2. 访问登录页面
            if not self.is_running:
                return False
            self.navigate_to_login()

            # 3. 填写登录表单
            if not self.is_running:
                return False
            self.fill_login_form()

            # 4. 点击登录按钮
            if not self.is_running:
                return False
            self.click_login_button()

            # 5. 处理验证码
            if not self.is_running:
                return False

            success = False
            for attempt in range(self.MAX_RETRIES):
                if not self.is_running:
                    return False

                self.log(f"验证码处理尝试 {attempt + 1}/{self.MAX_RETRIES}")

                if self.wait_for_captcha_manual():
                    success = True
                    break
                else:
                    if attempt < self.MAX_RETRIES - 1:
                        self.log(f"验证码失败，{self.RETRY_DELAY}秒后重试...")
                        time.sleep(self.RETRY_DELAY)
                        # 刷新页面重试
                        self.driver.refresh()
                        time.sleep(3)
                        self.fill_login_form()
                        self.click_login_button()

            if not success:
                self.log("验证码处理失败，登录终止", "ERROR")
                return False

            # 6. 访问目标页面
            if not self.is_running:
                return False

            if not self.navigate_to_target_page():
                self.log("访问目标页面失败", "ERROR")
                return False

            # 7. 截图
            if not self.is_running:
                return False

            screenshot_path = self.take_screenshot()
            if screenshot_path:
                self.log(f"任务完成！截图已保存: {screenshot_path}")
                return True
            else:
                self.log("截图失败", "ERROR")
                return False

        except Exception as e:
            self.log(f"执行过程中发生错误: {str(e)}", "ERROR")
            return False
        finally:
            self.cleanup()


class CallbackManager:
    """回调管理器，用于GUI与登录模块通信"""

    def __init__(self, gui_instance):
        self.gui = gui_instance

    def log_callback(self, message, level="INFO"):
        """日志回调"""
        if hasattr(self.gui, 'log_message'):
            # 根据日志级别添加前缀
            if level == "ERROR":
                message = f"❌ {message}"
            elif level == "WARNING":
                message = f"⚠️ {message}"
            elif level == "INFO":
                message = f"ℹ️ {message}"
            self.gui.log_message(message)

    def status_callback(self, message):
        """状态回调"""
        if hasattr(self.gui, 'status_queue'):
            self.gui.status_queue.put({"message": message})

    def countdown_callback(self, seconds):
        """倒计时回调"""
        if hasattr(self.gui, 'start_countdown_from_core'):
            self.gui.start_countdown_from_core(seconds)
