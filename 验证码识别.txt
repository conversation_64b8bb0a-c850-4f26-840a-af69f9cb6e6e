滑块<div class="tc-opera" id="tcOperation"><div class="tc-imgarea" id="tcImgArea"><div><img class="tc-bg-placeholder" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAFAQMAAACzVGSbAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAAAtJREFUCNdjYIABAAAKAAHn+Nr6AAAAAElFTkSuQmCC"></div><div class="tc-bg" id="slideBgWrap"><div class="tc-bg-img unselectable" id="slideBg" style="position: absolute; background-image: url(&quot;https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=1&amp;image=02610900006f6e4700000009dccbfd2e2ccd&amp;sess=s0jjsfMJfhSqrz76xU2-ihT2dyr4jzTQ8F_Yckz-4n-b-z_EQwfO9C5V1h2qFhZdAi1_N1l97jOvl6MC29KiohbdbajmnSlJXSgbqAtuTdWDVe2kglgtVRVHM_8RLGP4_Cv3CrHHJWR6t-kmmooOKtnWLxA69urefcbjT7oL9DDfAezwmldS0V3iwgWrK3y6iHzSHs3z2NXO8is-zmv3tnQzJClvQARQzaruuRJzqoXPbxHAI9RWqVsfuF-NNrxJCw1T27WfnWYzow7lOhwvucuY-KNh9-n9LWIeaCmJPE--o47BPSllNTzZti2o0crOkC-h7eQay8ydVyThbQJ017PQ9h9TYV4iB-8MIsfnVxGap428Jwy0sa9k-JceI6WbNEO_zLHyjWRrQ5F-SCL38OiPfTxRxtWSjRIbXrcposMkE*&quot;); background-position: 0px 0px; background-size: 100%; width: 313.333px; height: 223.809px; left: 0px; top: 0px; background-repeat: no-repeat; overflow: hidden; z-index: 1; opacity: 1;"></div></div><div class="tc-watermark-area" id="watermark" aria-label="混元AI生成" style="display: block; bottom: 1.31986rem;">混元AI生成</div></div><div class="tc-cover tc-loading"><div class="tc-loading-dots"><div class="tc-loading-dot dot0"></div><div class="tc-loading-dot dot1"></div><div class="tc-loading-dot dot2"></div><div class="tc-loading-dot dot3"></div></div></div><div class="tc-cover tc-fail" id="coverFail" alt="加载失败，请点击刷新" aria-label="加载失败，请点击刷新"><div class="tc-fail-text" id="statusFail" aria-hidden="true">加载失败，请点击刷新</div><div class="tc-fail-btn"></div></div><div class="tc-cover tc-success"><div class="tc-success-icon"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAMAAADVRocKAAAAzFBMVEUAAAAs0AAu1AAs0AAr0AAq1QAs0AAt0AAs0AAs0AAr0AAq0wAs0AAq0QAs0AAs0AAs0AAs0AAr0AAs0AAr0AAs0AAr0QAs0QAs0AApygAs0AAs0AAs0AAs0AAs0QAr0QAt0gAs0AAt0QAtzwAq0QArzwAs0AAqzQAs0QAs0AAs0AAs0AD////l+d/8/vty4FVG1iCI5W/X989G1h881BNy31My0gd34Vr5/vjt++qk65Gd6omO5naB42Za2jdL1ybR9cjA8bNp3kpZ2jY9PuX7AAAAK3RSTlMA/APuFwfUZln0OxLIVEbr49vZzsKxmW1MDvmsqIxpXiGejlA3NGgk1pyDVbbBQAAAArxJREFUaN7Nmody2kAURZ9QQRIyTTTRDDauV3GsFNzjlP//p8CQjAZLwLY3w/mAc9EWZvftIzGS8HIyitp2AAR2OxpNLsOETFHzex5K8Hp+Td/u9DsWdmJ1+o6OvTLoWjiA1R1UFPVutQ0h2lVX5ddXmxCmWZX+ijCGFHEot3BuIM1cYkmd21DAPhcd/TMociY0E7MIysQzgdltQIPGxSH/yRBaBPX9/lMLmgz9vX4Y4HTP+FgwgHWyy78IYIThYsf6bMAQjasyvxvDGLFLRW5gkHnJBMMohYl2bBjFdmibMQwz3vbXYZz61j+0B+N4FcqpgoFq7nebYKDpFj6A6xMqLbDQ+j8LAzAxoA1dMNH9t4kt6PL+/Ov5DwpYm+3chy7fH9M0vctQoE9rOtr+u3TN3Ts+0ln7a5a+f8NrcYzWx0nflD99QQF/FdAz5U8fUKC3CvCgw+fc/2WJAh5RYsr/6R4lJHTB6kdIPqsfU7pl9WNCY1Y/RhSx+hFRCzv4/fPx6Xmp6UeLbJTzsFnb3/T8sClAKcunNE9Q9yMglPMjTfMEdT+wOyBP0PFjzxDlCRr+YN8k5wnKfti7l2lWSFDwo0URhBIU/YhoDLEENT9GdAvBhIL/KwSYkA/RBBU/pnQB4QQFP0JKIJzwJu9HQuQJJ6Tyfk/g2JJp+NETOXhl6n74QkfHTNlv1cQOv5miHx3R43um5kdf+AKSKfktR/wKlSn40ZW5BL49rvealB8DqWvs/evLwxIytCrMF/FL9lICdzGEvZzDXZBiLqmN2IuC7GXNAnMYZM5cWvZcKuGKqTiesxjCCMGC94FiWGd+YvG5H4m4n7nYH+oOMouhTDwjAVz1x1L3OJ57VzhjSDN2SIa6Bym8+rE1Daxwp6JtD1P3OBs3+FtP8uaZa5RwnTfP6JOE0+32n6lo+89fOWa8/GcTqREAAAAASUVORK5CYII=" alt="success" srcset=""></div><div class="tc-success-text" id="statusSuccess" aria-live="assertive"></div></div><div class="tc-cover tc-error"><div class="tc-cover-icon"><svg id="errorIcon" width="37" height="25" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 37 25"><line x1="3" y1="3" x2="10" y2="9"></line><line x1="10" y1="9" x2="3" y2="15"></line><line x1="34" y1="3" x2="27" y2="9"></line><line x1="27" y1="9" x2="34" y2="15"></line><line x1="12" y1="23" x2="25" y2="23"></line></svg></div><div class="tc-error-text" id="statusError"></div></div><div class="tc-fg-item tc-slider-normal" aria-label="拖动下方滑块完成拼图" alt="拖动下方滑块完成拼图" style="left: 20.9821px; top: 187.44px; z-index: 2; width: 60.615px; height: 32.6389px; line-height: 32.6389px; background-color: rgb(0, 87, 212); box-shadow: rgba(0, 87, 212, 0.5) 0px 0px 4.66269px 0.466269px; cursor: pointer; opacity: 1;"><i class="tc-blank-text">&amp;nbsp;</i><img alt="slider" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAcAgMAAABuexVFAAAACVBMVEUAAADCwsL9/f1P0DqbAAAAAXRSTlMAQObYZgAAAB1JREFUGNNjCGVgYGANABKhyMwoEHMBkIgaZWIwAdyJJQnaJRg5AAAAAElFTkSuQmCC" class="tc-slider-bg unselectable" style="width: 17.3186px; height: 12.123px;"></div><div class="tc-fg-item" aria-label="拖动下方滑块完成拼图" alt="拖动下方滑块完成拼图" style="position: absolute; background-image: url(&quot;https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=0&amp;image=02610900006f6e4700000009dccbfd2e2ccd&amp;sess=s0jjsfMJfhSqrz76xU2-ihT2dyr4jzTQ8F_Yckz-4n-b-z_EQwfO9C5V1h2qFhZdAi1_N1l97jOvl6MC29KiohbdbajmnSlJXSgbqAtuTdWDVe2kglgtVRVHM_8RLGP4_Cv3CrHHJWR6t-kmmooOKtnWLxA69urefcbjT7oL9DDfAezwmldS0V3iwgWrK3y6iHzSHs3z2NXO8is-zmv3tnQzJClvQARQzaruuRJzqoXPbxHAI9RWqVsfuF-NNrxJCw1T27WfnWYzow7lOhwvucuY-KNh9-n9LWIeaCmJPE--o47BPSllNTzZti2o0crOkC-h7eQay8ydVyThbQJ017PQ9h9TYV4iB-8MIsfnVxGap428Jwy0sa9k-JceI6WbNEO_zLHyjWRrQ5F-SCL38OiPfTxRxtWSjRIbXrcposMkE*&quot;); background-position: 0px -196.766px; background-size: 317.996px 289.087px; width: 313.333px; height: 14.9206px; left: 0px; top: 196.766px; z-index: 1; opacity: 1;"></div><div class="tc-fg-item" aria-label="拖动下方滑块完成拼图" alt="拖动下方滑块完成拼图" style="position: absolute; background-image: url(&quot;https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=0&amp;image=02610900006f6e4700000009dccbfd2e2ccd&amp;sess=s0jjsfMJfhSqrz76xU2-ihT2dyr4jzTQ8F_Yckz-4n-b-z_EQwfO9C5V1h2qFhZdAi1_N1l97jOvl6MC29KiohbdbajmnSlJXSgbqAtuTdWDVe2kglgtVRVHM_8RLGP4_Cv3CrHHJWR6t-kmmooOKtnWLxA69urefcbjT7oL9DDfAezwmldS0V3iwgWrK3y6iHzSHs3z2NXO8is-zmv3tnQzJClvQARQzaruuRJzqoXPbxHAI9RWqVsfuF-NNrxJCw1T27WfnWYzow7lOhwvucuY-KNh9-n9LWIeaCmJPE--o47BPSllNTzZti2o0crOkC-h7eQay8ydVyThbQJ017PQ9h9TYV4iB-8MIsfnVxGap428Jwy0sa9k-JceI6WbNEO_zLHyjWRrQ5F-SCL38OiPfTxRxtWSjRIbXrcposMkE*&quot;); background-position: -65.2777px -228.472px; background-size: 317.996px 289.087px; width: 55.9523px; height: 55.9523px; left: 23.3135px; top: 60.1487px; z-index: 1; cursor: pointer; opacity: 1;"></div></div>



通过浏览器网络查看

请求 URL
https://turing.captcha.qcloud.com/cap_union_prehandle?aid=199128792&protocol=https&accver=1&showtype=embed&ua=**************************************************************************************************************************************************************************************************%3D%3D&noheader=1&fb=0&aged=0&enableAged=0&enableDarkMode=0&grayscale=1&clientype=2&cap_cd=&uid=&lang=zh-cn&entry_url=https%3A%2F%2Fauth.smartedu.cn%2Fuias%2Flogin&elder_captcha=0&js=%2Ftcaptcha-frame.3a9b8e84.js&login_appid=&wb=1&subsid=1&callback=_aq_508862&sess=
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
connection
keep-alive
content-encoding
gzip
content-type
text/javascript;charset=utf-8
date
Thu, 31 Jul 2025 04:15:53 GMT
p3p
CP=CAO PSA OUR
pragma
No-cache
server
Trpc httpd
server
tencent http server
transfer-encoding
chunked
accept
*/*
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
connection
keep-alive
host
turing.captcha.qcloud.com
referer
https://auth.smartedu.cn/
sec-ch-ua
"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
sec-ch-ua-mobile
?1
sec-ch-ua-platform
"Android"
sec-fetch-dest
script
sec-fetch-mode
no-cors
sec-fetch-site
cross-site
sec-fetch-storage-access
active
user-agent
Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********



负载：
aid
199128792
protocol
https
accver
1
showtype
embed
ua
**************************************************************************************************************************************************************************************************==
noheader
1
fb
0
aged
0
enableAged
0
enableDarkMode
0
grayscale
1
clientype
2
cap_cd
uid
lang
zh-cn
entry_url
https://auth.smartedu.cn/uias/login
elder_captcha
0
js
/tcaptcha-frame.3a9b8e84.js
login_appid
wb
1
subsid
1
callback
_aq_508862

响应：

_aq_508862({
    "state": 1,
    "ticket": "",
    "capclass": "1",
    "subcapclass": "2401",
    "src_1": "cap_union_new_show",
    "src_2": "template/new_placeholder.html",
    "src_3": "template/new_slide_placeholder.html",
    "sess": "s0J1-4hBPosTJd39Y_0MLQnSdN-HhLUjc7GksZsVTyZZTm-jjlGvLglSEV9IUDzgs45ttrVyrLEIXl4ePWMxdDxdMyovCwIhlLti2vzalP0nkSMLVKLZNseFk_TYUlYOUW3-WnQK1H_fSfFWjS8a8ph4e2pq23MvqmzNIKh_g229ja6k6koYjnnobtZl56oVp-l9lvYEZIik2_vXUg5lGSYoX7EGsZze9oUEUrXPPzJjEpMujd2YZmWjfiodmLSZz9hedbivGo7k0MPoBMerjO4w5LOK1U4WDpYMBk1Lg7_dH-hr9JqmvdZLgtnzEt5kNxKr_CNHCMTyS1CqtNb3LyRMeYyQxD-hLR5EFnvkvBNfftBQmAzQGsSDJLaZM2bBPKOkEwKKxnypL7C7lUdDaIAZPaexZliJufgCyjrPNkVIk*",
    "randstr": "",
    "sid": "7356538068147294208",
    "log_js": "",
    "data": {
        "comm_captcha_cfg": {
            "tdc_path": "/tdc.js?app_data=7356538068147294208&t=372596311",
            "feedback_url": "https://support.qq.com/products/304457",
            "pow_cfg": {
                "prefix": "61bc81c1b6e532ed#",
                "md5": "f745c3c648fe30a9755eaa38ace7aaf7"
            }
        },
        "dyn_show_info": {
            "lang": "zh-cn",
            "instruction": "拖动下方滑块完成拼图",
            "bg_elem_cfg": {
                "sprite_pos": [0, 0],
                "size_2d": [672, 480],
                "img_url": "/cap_union_new_getcapbysig?img_index=1&image=02610900006f6e4700000009dccbfd2e2ccd&sess=s0jjsfMJfhSqrz76xU2-ihT2dyr4jzTQ8F_Yckz-4n-b-z_EQwfO9C5V1h2qFhZdAi1_N1l97jOvl6MC29KiohbdbajmnSlJXSgbqAtuTdWDVe2kglgtVRVHM_8RLGP4_Cv3CrHHJWR6t-kmmooOKtnWLxA69urefcbjT7oL9DDfAezwmldS0V3iwgWrK3y6iHzSHs3z2NXO8is-zmv3tnQzJClvQARQzaruuRJzqoXPbxHAI9RWqVsfuF-NNrxJCw1T27WfnWYzow7lOhwvucuY-KNh9-n9LWIeaCmJPE--o47BPSllNTzZti2o0crOkC-h7eQay8ydVyThbQJ017PQ9h9TYV4iB-8MIsfnVxGap428Jwy0sa9k-JceI6WbNEO_zLHyjWRrQ5F-SCL38OiPfTxRxtWSjRIbXrcposMkE*"
            },
            "fg_elem_list": [{
                "id": 3,
                "sprite_pos": [0, 422],
                "size_2d": [672, 32],
                "init_pos": [0, 422]
            }, {
                "id": 1,
                "sprite_pos": [140, 490],
                "size_2d": [120, 120],
                "init_pos": [50, 129],
                "move_cfg": {
                    "track_limit": "x>=50&&x<=552",
                    "move_factor": [1, 0],
                    "data_type": ["DynAnswerType_POS"]
                }
            }, {
                "id": 2,
                "sprite_pos": [0, 490],
                "size_2d": [130, 70],
                "init_pos": [45, 402],
                "move_cfg": {
                    "track_limit": "x>=50&&x<=552",
                    "move_factor": [1, 0]
                },
                "type": "slider"
            }],
            "fg_binding_list": [{
                "master": 1,
                "slave": 2,
                "bind_type": "DynBindType_DX_DX",
                "bind_factor": 1
            }, {
                "master": 2,
                "slave": 1,
                "bind_type": "DynBindType_DX_DX",
                "bind_factor": 1
            }],
            "sprite_url": "/cap_union_new_getcapbysig?img_index=0&image=02610900006f6e4700000009dccbfd2e2ccd&sess=s0jjsfMJfhSqrz76xU2-ihT2dyr4jzTQ8F_Yckz-4n-b-z_EQwfO9C5V1h2qFhZdAi1_N1l97jOvl6MC29KiohbdbajmnSlJXSgbqAtuTdWDVe2kglgtVRVHM_8RLGP4_Cv3CrHHJWR6t-kmmooOKtnWLxA69urefcbjT7oL9DDfAezwmldS0V3iwgWrK3y6iHzSHs3z2NXO8is-zmv3tnQzJClvQARQzaruuRJzqoXPbxHAI9RWqVsfuF-NNrxJCw1T27WfnWYzow7lOhwvucuY-KNh9-n9LWIeaCmJPE--o47BPSllNTzZti2o0crOkC-h7eQay8ydVyThbQJ017PQ9h9TYV4iB-8MIsfnVxGap428Jwy0sa9k-JceI6WbNEO_zLHyjWRrQ5F-SCL38OiPfTxRxtWSjRIbXrcposMkE*",
            "color_scheme": "#0057d4"
        }
    },
    "uip": "**************"
})

